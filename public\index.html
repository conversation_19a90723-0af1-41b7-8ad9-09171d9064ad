<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>XennyGames — Frisch</title>

    <!-- Optional nice font (replace or remove if you prefer local fonts) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800&display=swap" rel="stylesheet">

    <style>
        :root{
            --bg-1: #0f1724; /* deep */
            --bg-2: #141621; /* mid */
            --card: rgba(255,255,255,0.04);
            --glass: rgba(255,255,255,0.06);
            --muted: rgba(255,255,255,0.6);
            --accent: #9146ff; /* twitch */
            --accent-2: #5b3fb2;
            --success: #4CAF50;
            --danger: #f44336;
        }

        *{box-sizing:border-box;margin:0;padding:0}
        html,body{height:100%}
        body{
            font-family: 'Inter', system-ui, -apple-system, 'Segoe UI', Roboto, "Helvetica Neue", Arial;
            background: radial-gradient(1200px 600px at 10% 10%, rgba(145,70,255,0.06), transparent 8%),
                        linear-gradient(180deg, var(--bg-1) 0%, var(--bg-2) 100%);
            color: #e9eef8;
            -webkit-font-smoothing:antialiased;
            -moz-osx-font-smoothing:grayscale;
            padding:28px;
            min-height:100vh;
        }

        .container{
            max-width:1200px;
            margin:0 auto;
        }

        .header{
            display:flex;
            gap:16px;
            align-items:center;
            justify-content:space-between;
            margin-bottom:18px;
            flex-wrap:wrap;
        }

        h1{
            font-size:1.9rem;
            letter-spacing:0.4px;
            display:flex;
            align-items:center;
            gap:10px;
            color: white;
            font-weight:700;
        }

        /* Stream banner (new) */
        .stream-banner-wrap{display:flex;justify-content:flex-end;margin-bottom:12px}
        .stream-banner{
            display:flex;align-items:center;gap:12px;padding:10px 14px;border-radius:12px;
            background:linear-gradient(90deg,var(--accent),var(--accent-2));
            color:white;box-shadow:0 8px 30px rgba(8,6,23,0.6);
            border:1px solid rgba(255,255,255,0.06);
            transform:translateY(0);
        }
        .stream-left{display:flex;align-items:center;gap:12px}
        .stream-logo{width:44px;height:44px;border-radius:8px;background:rgba(0,0,0,0.12);display:flex;align-items:center;justify-content:center;font-weight:800}

        .stream-meta{display:flex;flex-direction:column}
        .stream-title{font-weight:700;font-size:0.95rem}
        .stream-cta{font-size:0.85rem;opacity:0.95}

        .on-air{display:flex;align-items:center;gap:8px}
        .on-air .dot{width:12px;height:12px;border-radius:999px;background:#ff3b30;box-shadow:0 0 10px rgba(255,59,48,0.75);}
        /* blink animation */
        @keyframes stream-pulse{0%{transform:scale(1);opacity:1}50%{transform:scale(1.18);opacity:0.7}100%{transform:scale(1);opacity:1}}
        .on-air.on .dot{animation:stream-pulse 1.2s infinite}
        .on-air.off .dot{background:rgba(255,255,255,0.18);box-shadow:none;opacity:0.6}

        .stream-link{display:inline-flex;align-items:center;gap:8px;padding:6px 10px;border-radius:8px;background:rgba(0,0,0,0.12);text-decoration:none;color:white;font-weight:600;border:1px solid rgba(255,255,255,0.04)}
        .stream-link:hover{transform:translateY(-2px)}

        /* Header right - auth */
        .auth-section{display:flex;align-items:center;gap:10px}
        .twitch-login-btn{background:linear-gradient(45deg,#9146ff,#772ce8);color:white;border:none;padding:10px 18px;border-radius:20px;font-weight:700;cursor:pointer;box-shadow:0 10px 30px rgba(145,70,255,0.14)}
        .twitch-login-btn:hover{transform:translateY(-2px)}

        .user-section{display:flex;align-items:center;gap:12px;background:linear-gradient(180deg,rgba(255,255,255,0.02),rgba(255,255,255,0.01));padding:8px 12px;border-radius:20px;border:1px solid rgba(255,255,255,0.04)}
        .user-avatar{width:42px;height:42px;border-radius:50%;border:2px solid rgba(255,255,255,0.06)}
        .user-name{font-weight:700}
        .admin-badge{background:linear-gradient(90deg,#ffd700,#ffed4e);color:#222;padding:4px 8px;border-radius:12px;font-weight:800}

        /* Admin tools */
        .admin-panel{display:none;margin-bottom:18px;padding:14px;border-radius:12px;background:linear-gradient(180deg,rgba(255,255,255,0.02),rgba(255,255,255,0.01));border:1px solid rgba(255,255,255,0.04)}
        .admin-controls{display:flex;gap:10px;flex-wrap:wrap}
        .admin-controls input{padding:8px 12px;border-radius:8px;border:1px solid rgba(255,255,255,0.04);background:transparent;color:inherit}
        .action-btn{padding:8px 12px;border-radius:10px;border:1px solid rgba(255,255,255,0.04);background:transparent;color:inherit;cursor:pointer}
        .action-btn.spieleliste{border-color:rgba(33,150,243,0.12)}

        /* Dashboard grid */
        .dashboard{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:20px}
        .game-box{background:linear-gradient(180deg,rgba(255,255,255,0.03),rgba(255,255,255,0.02));border-radius:12px;padding:12px;border:1px solid rgba(255,255,255,0.03)}
        .box-header{display:flex;justify-content:space-between;align-items:center;padding-bottom:8px;border-bottom:1px dashed rgba(255,255,255,0.03);margin-bottom:10px}
        .box-header h2{font-size:1.05rem}
        .game-count{font-size:0.85rem;opacity:0.8}

        .game-container{display:grid;grid-template-columns:repeat(2,1fr);gap:12px;max-height:520px;overflow:auto;padding:6px}
        .game-boxart{background:var(--card);border-radius:10px;overflow:hidden;display:flex;flex-direction:column;cursor:pointer;transition:transform 0.15s ease,box-shadow 0.15s ease}
        .game-boxart:hover{transform:translateY(-6px);box-shadow:0 10px 30px rgba(2,6,23,0.55)}
        .boxart-image{width:100%;height:180px;object-fit:cover;background:#111}
        .boxart-footer{padding:10px;display:flex;flex-direction:column;gap:8px}
        .boxart-name{font-weight:700;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}
        .boxart-meta{display:flex;justify-content:space-between;align-items:center;gap:8px}

        /* small helpers */
        .boxart-votes{display:flex;align-items:center;gap:6px}
        .boxart-score{font-weight:700}

        .connection-status{position:fixed;right:18px;top:18px;padding:8px 12px;border-radius:999px;font-weight:700;background:rgba(0,0,0,0.45);backdrop-filter:blur(6px);z-index:999}
        .connection-status.connected{background:linear-gradient(90deg,#2ecc71,#27ae60)}
        .connection-status.disconnected{background:linear-gradient(90deg,#ff5b5b,#ff3b3b)}

        /* responsive tweaks */
        @media (max-width:720px){
            .game-container{grid-template-columns:repeat(1,1fr)}
            .stream-banner-wrap{justify-content:center}
            .header{align-items:flex-start}
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🎮 XennyGames</h1>

            <div class="auth-section">
                <div id="login-section" class="login-section">
                    <button id="twitch-login-btn" class="twitch-login-btn">🎮 Mit Twitch anmelden</button>
                </div>

                <div id="user-section" class="user-section" style="display:none">
                    <div style="display:flex;align-items:center;gap:10px">
                        <img id="user-avatar" class="user-avatar" src="" alt="Avatar" />
                        <div style="display:flex;flex-direction:column;line-height:1">
                            <span id="user-name" class="user-name"></span>
                            <small style="opacity:0.8;font-size:0.78rem">Angemeldet</small>
                        </div>
                    </div>
                    <span id="admin-badge" class="admin-badge" style="display:none">👑 Admin</span>
                    <button id="logout-btn" class="action-btn" style="margin-left:8px">Abmelden</button>
                </div>
            </div>
        </div>

        <!-- Stream banner (NEW) -->
        <div class="stream-banner-wrap">
            <div id="stream-banner" class="stream-banner" style="display:none" aria-live="polite">
                <div class="stream-left">
                    <div class="stream-logo">TG</div>
                    <div class="stream-meta">
                        <div class="stream-title">XennyGames — Live auf Twitch</div>
                        <div class="stream-cta">Klicke zum Zuschauen →</div>
                    </div>
                </div>

                <div style="display:flex;align-items:center;gap:10px">
                    <a id="stream-link" class="stream-link" href="https://twitch.tv/XennyGames" target="_blank" rel="noopener noreferrer">Zu Twitch</a>
                    <div id="on-air" class="on-air off" title="Stream Status">
                        <div class="dot"></div>
                        <div style="font-weight:700;letter-spacing:0.4px">OFF AIR</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Admin tools -->
        <div id="admin-panel" class="admin-panel">
            <h3>Admin Tools</h3>
            <div class="admin-controls">
                <input type="text" id="new-game-name" placeholder="Spielname" />
                <button type="button" onclick="searchIGDB()" class="action-btn spieleliste">IGDB Suche</button>
                <button type="button" onclick="addGame()" class="action-btn spieleliste">Manuell hinzufügen</button>
                <button type="button" onclick="syncWithConfigServer()" class="action-btn" style="background:var(--accent);color:white">📡 Config Sync</button>

                <!-- Small admin-only control to toggle the on-air indicator locally for testing -->
                <div id="admin-stream-control" style="display:none;align-items:center;gap:8px">
                    <label style="font-weight:700;margin-left:8px">Stream On-Air:</label>
                    <button class="action-btn" onclick="setStreamOnAir(!streamOnAir)">Toggle Stream</button>
                </div>
            </div>

            <div id="igdb-search-results" class="igdb-search-results" style="display:none;margin-top:12px">
                <h4>IGDB Suchergebnisse</h4>
                <div id="igdb-results-container" class="igdb-results-container"></div>
            </div>
        </div>

        <div id="loading" class="loading">Lade Spiele...</div>

        <div id="error" class="error" style="display:none">
            <h3>Fehler beim Laden der Spiele</h3>
            <p id="error-message"></p>
            <button class="refresh-btn" onclick="loadGames()">Erneut versuchen</button>
        </div>

        <div id="dashboard" class="dashboard" style="display:none">
            <div class="game-box gespielt" data-category="Gespielt">
                <div class="box-header">
                    <h2>✅ Gespielt</h2>
                    <div class="game-count" id="gespielt-count">0 Spiele</div>
                </div>
                <div id="gespielt-games" class="game-container"></div>
            </div>

            <div class="game-box wird-gespielt" data-category="Spielt gerade">
                <div class="box-header">
                    <h2>🎯 Wird gespielt</h2>
                    <div class="game-count" id="wird-gespielt-count">0 Spiele</div>
                </div>
                <div id="wird-gespielt-games" class="game-container"></div>
            </div>

            <div class="game-box spieleliste" data-category="Auf der Spieleliste">
                <div class="box-header">
                    <h2>📋 Auf der Spieleliste</h2>
                    <div class="game-count" id="spieleliste-count">0 Spiele</div>
                </div>
                <div id="spieleliste-games" class="game-container"></div>
            </div>
        </div>
    </div>

    <div id="connection-status" class="connection-status disconnected">Verbindung getrennt</div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        /* ---------- STREAM ON-AIR FLAG (toggle this var to show the banner) ---------- */
        // For now: flip this value to true/false to show the stream banner and ON AIR indicator.
        // Later you can update this from your backend via a webhook/socket event.
        const STREAM_ON_AIR = false; // <-- set true to show "on-air" immediately
        let streamOnAir = STREAM_ON_AIR;

        /* ---------- existing app variables (kept from original) ---------- */
        let currentUser = null;
        let gameData = null;
        let isAdmin = false;
        let socket = null;
        let reconnectAttempts = 0;
        const MAX_RECONNECT_ATTEMPTS = 5;
        const RECONNECT_DELAY = 3000;

        // DOM
        const loginSection = document.getElementById('login-section');
        const userSection = document.getElementById('user-section');
        const userAvatar = document.getElementById('user-avatar');
        const userName = document.getElementById('user-name');
        const adminBadge = document.getElementById('admin-badge');
        const logoutBtn = document.getElementById('logout-btn');
        const adminPanel = document.getElementById('admin-panel');
        const dashboard = document.getElementById('dashboard');
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const errorMessage = document.getElementById('error-message');
        const connectionStatus = document.getElementById('connection-status');
        const twitchLoginBtn = document.getElementById('twitch-login-btn');

        // Stream elements
        const streamBanner = document.getElementById('stream-banner');
        const onAirEl = document.getElementById('on-air');
        const streamLink = document.getElementById('stream-link');
        const adminStreamControl = document.getElementById('admin-stream-control');

        // Init
        async function init(){
            setupEventListeners();
            await checkSession();
            connectToServer();
            renderStreamBanner();
        }

        function setupEventListeners(){
            twitchLoginBtn.addEventListener('click', handleTwitchLogin);
            logoutBtn.addEventListener('click', logout);
        }

        // Render stream banner according to streamOnAir variable
        function renderStreamBanner(){
            if(!streamOnAir){
                // show banner but indicate off-air OR hide entirely — choose to show banner when off to advertise schedule
                streamBanner.style.display = 'flex';
                onAirEl.classList.remove('on');
                onAirEl.classList.add('off');
                onAirEl.querySelector('div:nth-child(2)').textContent = 'OFF AIR';
            } else {
                streamBanner.style.display = 'flex';
                onAirEl.classList.remove('off');
                onAirEl.classList.add('on');
                onAirEl.querySelector('div:nth-child(2)').textContent = 'LIVE';
            }

            // admin-only control visibility
            if(isAdmin){
                adminStreamControl.style.display = 'flex';
            } else {
                adminStreamControl.style.display = 'none';
            }
        }

        // Allow toggling from console or other code while the site is running
        window.setStreamOnAir = function(val){
            streamOnAir = !!val;
            renderStreamBanner();
        };

        // For future backend integration: we listen for a socket event named 'stream_status'
        // Your backend webhook can emit this socket.io event to update connected clients live.
        function handleStreamSocketEvent(data){
            // expected payload: { on: true/false }
            if(typeof data?.on !== 'undefined'){
                window.setStreamOnAir(data.on);
            }
        }

        /* -------------------- Session & auth (kept mostly intact) -------------------- */
        async function checkSession(){
            try{
                const response = await fetch('/api/session');
                if(response.ok){
                    const user = await response.json();
                    setUser(user);
                }
            }catch(e){
                console.log('No session found');
                renderStreamBanner();
            }
        }

        function handleTwitchLogin(){
            const scope = encodeURIComponent('openid');
            window.location = `https://id.twitch.tv/oauth2/authorize?client_id=******************************&redirect_uri=https://games.xenny.news/&response_type=token&scope=${scope}&force_verify=true`;
        }

        async function handleTwitchLoginToken(token){
            try{
                const response = await fetch('/api/login',{
                    method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({token})
                });
                if(!response.ok){
                    const err = await response.json(); throw new Error(err.error||'Login failed');
                }
                const user = await response.json(); setUser(user); window.location.hash='';
            }catch(e){console.error('Login failed',e);showError(`Login fehlgeschlagen: ${e.message}`)}
        }

        function setUser(user){
            currentUser = user; isAdmin = !!user.isAdmin;
            loginSection.style.display = 'none';
            userSection.style.display = 'flex';

            let avatar = user.avatar || `https://static-cdn.jtvnw.net/jtv_user_pictures/${user.username}-profile_image-70x70.png`;
            avatar = avatar.replace('http://','https://');
            userAvatar.onerror = function(){this.src='https://static-cdn.jtvnw.net/jtv_user_pictures/unknown-profile_image-70x70.png'};
            userAvatar.src = avatar;
            userName.textContent = user.display_name || user.username;

            if(user.isAdmin){adminBadge.style.display='inline-block';adminPanel.style.display='block';}
            renderGames(); renderStreamBanner();
        }

        async function logout(){
            try{await fetch('/api/logout',{method:'POST'})}catch(e){console.error('Logout failed',e)}
            currentUser=null;isAdmin=false;userSection.style.display='none';loginSection.style.display='block';adminBadge.style.display='none';adminPanel.style.display='none';renderGames();renderStreamBanner();
        }

        /* -------------------- sockets -------------------- */
        function connectToServer(){
            if(socket) socket.disconnect();
            try{
                socket = io({
                    reconnection:true,
                    reconnectionAttempts:MAX_RECONNECT_ATTEMPTS,
                    reconnectionDelay:RECONNECT_DELAY,
                    reconnectionDelayMax:5000,
                    randomizationFactor:0.5
                });

                socket.on('connect',()=>{reconnectAttempts=0;connectionStatus.textContent='Verbunden';connectionStatus.className='connection-status connected';hideError();});
                socket.on('init',(data)=>{gameData=data;renderGames();});
                socket.on('update',(data)=>{gameData=data;renderGames();});
                socket.on('stream_status',(data)=>{handleStreamSocketEvent(data)});
                socket.on('disconnect',reason=>{console.log('Disconnected',reason);connectionStatus.textContent='Verbindung getrennt';connectionStatus.className='connection-status disconnected';});
                socket.on('reconnect_attempt',attempt=>{reconnectAttempts=attempt;connectionStatus.textContent=`Verbindung wird hergestellt... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`});
                socket.on('reconnect_failed',()=>{connectionStatus.textContent='Verbindung fehlgeschlagen. Seite neu laden.';showError('Verbindung zum Server verloren. Bitte Seite neu laden.')});
                socket.on('connect_error',error=>{console.error('Connection error:',error;showError(`Verbindungsfehler: ${error.message}`)});

            }catch(e){console.warn('Socket.IO not available',e)}
        }

        /* -------------------- Rendering games (kept largely intact) -------------------- */
        function renderGames(){
            if(!gameData){loading.style.display='block';dashboard.style.display='none';return}
            loading.style.display='none';dashboard.style.display='grid';
            renderGameCategory('gespielt',gameData.games,'Gespielt');
            renderGameCategory('wird-gespielt',gameData.games,'Spielt gerade');
            renderGameCategory('spieleliste',gameData.games,'Auf der Spieleliste');
        }

        function renderGameCategory(containerId,games,category){
            const container = document.getElementById(`${containerId}-games`);
            const countElement = document.getElementById(`${containerId}-count`);
            if(!container || !countElement) return;
            container.innerHTML='';
            const filteredGames = Object.entries(games).filter(([name,game])=>game.overlay===category);

            if(category==='Auf der Spieleliste'){
                filteredGames.sort((a,b)=>{const scoreA=gameData.votes[a[0]]?.score||0;const scoreB=gameData.votes[b[0]]?.score||0;if(scoreA!==scoreB) return scoreB-scoreA;return b[1].createdAt.localeCompare(a[1].createdAt)});
            }else if(category==='Gespielt'){
                filteredGames.sort((a,b)=>{const dateA=b[1].movedToCurrentCategoryAt||b[1].createdAt;const dateB=a[1].movedToCurrentCategoryAt||a[1].createdAt;return dateA.localeCompare(dateB)})
            }else{filteredGames.sort((a,b)=>b[1].createdAt.localeCompare(a[1].createdAt))}

            countElement.textContent = `${filteredGames.length} Spiel${filteredGames.length!==1?'e':''}`;
            filteredGames.forEach(([gameName,gameInfo])=>{const gameElement=createGameElement(gameName,gameInfo,containerId);container.appendChild(gameElement)});
        }

        function createGameElement(gameName,gameInfo,categoryId){
            const gameDiv = document.createElement('div');
            gameDiv.className='game-boxart';
            gameDiv.setAttribute('data-game',gameName);
            gameDiv.draggable = isAdmin;

            const votes = gameData.votes[gameName] || {score:0,votes:{}};
            const userVote = currentUser ? votes.votes[currentUser.username] : null;

            const img = document.createElement('img');
            img.className='boxart-image';
            img.alt = gameName;
            img.onerror = function(){this.onerror=null;this.src='https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png'};
            const coverUrl = gameInfo.cover_url || 'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';
            img.src = coverUrl.startsWith('http') ? coverUrl : 'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';

            const playlistSection = createPlaylistSection(gameName,gameInfo.youtube_playlist);

            gameDiv.innerHTML = `
                <div class="boxart-footer">
                    <div class="boxart-name" title="${gameName}">${gameName}</div>
                    <div class="boxart-meta">
                        ${categoryId==='gespielt'?`<span class="boxart-status status-date">${gameInfo.movedToCurrentCategoryAt?new Date(gameInfo.movedToCurrentCategoryAt).toLocaleDateString('de-DE'):(gameInfo.createdAt?new Date(gameInfo.createdAt).toLocaleDateString('de-DE'):'Unbekannt')}</span>`:categoryId==='wird-gespielt'?`<span class="boxart-status ${(gameInfo.movedToCurrentCategoryAt||gameInfo.createdAt)?'status-date':'status-unknown'}">${gameInfo.movedToCurrentCategoryAt?new Date(gameInfo.movedToCurrentCategoryAt).toLocaleDateString('de-DE'):(gameInfo.createdAt?new Date(gameInfo.createdAt).toLocaleDateString('de-DE'):'?')}</span>`:`<span class="boxart-status ${gameInfo.owned?'status-owned':'status-not-owned'}">${gameInfo.owned?'Gekauft':'Nicht Gekauft'}</span>`}

                        ${categoryId==='spieleliste'?`<div class="boxart-votes"><button class="boxart-vote-btn upvote ${userVote==='up'?'active':''}" onclick="vote('${gameName.replace(/'/g,"\\'")}','up',event)" ${currentUser?'':'disabled'}>▲</button><span class="boxart-score ${getScoreClass(votes.score)}">${votes.score}</span><button class="boxart-vote-btn downvote ${userVote==='down'?'active':''}" onclick="vote('${gameName.replace(/'/g,"\\'")}','down',event)" ${currentUser?'':'disabled'}>▼</button></div>`:playlistSection?`<div class="boxart-votes">${playlistSection}</div>`:`<div class="boxart-votes"><span class="boxart-score ${getScoreClass(votes.score)}">${votes.score}</span></div>`}
                    </div>

                    ${categoryId==='spieleliste' && playlistSection?playlistSection:''}

                    ${isAdmin?`<div class="boxart-actions"><select class="boxart-category-select" onchange="changeCategory('${gameName.replace(/'/g,"\\'")}',this.value)"><option value="Gespielt" ${gameInfo.overlay==='Gespielt'?'selected':''}>✅ Gespielt</option><option value="Spielt gerade" ${gameInfo.overlay==='Spielt gerade'?'selected':''}>🎯 Spielt</option><option value="Auf der Spieleliste" ${gameInfo.overlay==='Auf der Spieleliste'?'selected':''}>📋 Liste</option></select>${(categoryId!=='gespielt' && categoryId!=='wird-gespielt')?`<button class="action-btn" onclick="toggleOwned('${gameName.replace(/'/g,"\\'")}',event)">Besitz ändern</button>`:''}<button class="action-btn" style="background:var(--danger);color:white" onclick="removeGame('${gameName.replace(/'/g,"\\'")}',event)">× Löschen</button>${(categoryId!=='spieleliste' && !playlistSection)?`<button class="action-btn" onclick="editPlaylist('${gameName.replace(/'/g,"\\'")}',event)">+ Playlist</button>`:''}</div>`:''}
                </div>
            `;

            if(isAdmin){
                gameDiv.addEventListener('dragstart',handleDragStart);
                gameDiv.addEventListener('dragend',handleDragEnd);
            }

            gameDiv.insertBefore(img,gameDiv.firstChild);
            return gameDiv;
        }

        function createPlaylistSection(gameName,playlistUrl){
            if(playlistUrl){
                const youtubeLogoSvg = `<svg class="youtube-logo" viewBox="0 0 24 24" fill="currentColor" width="18" height="18"><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/></svg>`;
                return `<a href="${playlistUrl}" target="_blank" class="playlist-link">${youtubeLogoSvg}</a>${isAdmin?`<button class="playlist-btn edit" onclick="editPlaylist('${gameName.replace(/'/g,"\\'")}',event)" title="Playlist bearbeiten">✏️</button>`:''}`;
            } else if(isAdmin){
                return `<button class="playlist-btn edit" onclick="editPlaylist('${gameName.replace(/'/g,"\\'")}',event)">+ Playlist hinzufügen</button>`;
            }
            return '';
        }

        function editPlaylist(gameName,event){event.stopPropagation();if(!isAdmin)return;const gameElement=document.querySelector(`.game-boxart[data-game="${gameName}"]`);const playlistContainer=gameElement.querySelector('.boxart-votes')||gameElement.querySelector('.playlist-section');const currentUrl=gameData.games[gameName]?.youtube_playlist||'';playlistContainer.innerHTML=`<input type="text" class="playlist-input" value="${currentUrl}" placeholder="YouTube Playlist URL eingeben..." onkeypress="handlePlaylistKeypress(event,'${gameName.replace(/'/g,"\\'")}')"><div class="playlist-actions"><button class="playlist-btn save" onclick="savePlaylist('${gameName.replace(/'/g,"\\'")}',event)">💾 Speichern</button><button class="playlist-btn cancel" onclick="cancelPlaylistEdit('${gameName.replace(/'/g,"\\'")}',event)">❌ Abbrechen</button></div>`;const input=playlistContainer.querySelector('.playlist-input');input.focus();input.select()}

        function handlePlaylistKeypress(event,gameName){if(event.key==='Enter'){savePlaylist(gameName,event)}else if(event.key==='Escape'){cancelPlaylistEdit(gameName,event)}}

        async function savePlaylist(gameName,event){event.stopPropagation();if(!isAdmin)return;const gameElement=document.querySelector(`.game-boxart[data-game="${gameName}"]`);const input=gameElement.querySelector('.playlist-input');const playlistUrl=input.value.trim();try{const response=await fetch('/api/game/playlist',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({gameName,playlistUrl})});if(!response.ok){const err=await response.json();throw new Error(err.error||'Failed to update playlist');}}catch(error){console.error('Playlist update failed:',error);alert(`Playlist aktualisieren fehlgeschlagen: ${error.message}`);cancelPlaylistEdit(gameName,event)}}

        function cancelPlaylistEdit(gameName,event){event.stopPropagation();const gameElement=document.querySelector(`.game-boxart[data-game="${gameName}"]`);const playlistContainer=gameElement.querySelector('.boxart-votes');const currentUrl=gameData.games[gameName]?.youtube_playlist;if(currentUrl){playlistContainer.innerHTML=createPlaylistSection(gameName,currentUrl)}else{playlistContainer.innerHTML=`<span class="boxart-score ${getScoreClass(gameData.votes[gameName]?.score||0)}">${gameData.votes[gameName]?.score||0}</span>`}}

        function getScoreClass(score){if(score>0) return 'score-positive'; if(score<0) return 'score-negative'; return 'score-zero'}

        async function vote(gameName,voteType,event){
            event.stopPropagation();
            if(!currentUser) return;
            try{
                const currentVotes = gameData.votes[gameName] || {score:0,votes:{}};
                const newVoteType = currentVotes.votes[currentUser.username]===voteType?null:voteType;
                const response = await fetch('/api/vote',{
                    method:'POST',
                    headers:{'Content-Type':'application/json'},
                    body:JSON.stringify({gameName,voteType:newVoteType})
                });
                if(!response.ok){
                    const err=await response.json();
                    throw new Error(err.error||'Vote failed')
                }
                if(newVoteType===null){
                    delete currentVotes.votes[currentUser.username]
                }else{
                    currentVotes.votes[currentUser.username]=newVoteType
                }
                currentVotes.score = Object.values(currentVotes.votes).reduce((sum,vote)=>sum + (vote==='up'?1:-1),0);
                const gameElement=document.querySelector(`.game-boxart[data-game="${gameName}"]`);
                if(gameElement){
                    const upBtn=gameElement.querySelector('.upvote');
                    const downBtn=gameElement.querySelector('.downvote');
                    const scoreSpan=gameElement.querySelector('.boxart-score');
                    upBtn.classList.toggle('active',currentVotes.votes[currentUser.username]==='up');
                    downBtn.classList.toggle('active',currentVotes.votes[currentUser.username]==='down');
                    scoreSpan.textContent=currentVotes.score;
                    scoreSpan.className=`boxart-score ${getScoreClass(currentVotes.score)}`
                }
            }catch(error){
                console.error('Vote failed',error);
                alert(`Abstimmung fehlgeschlagen: ${error.message}`)
            }
        } // <-- This closing brace was missing in the original code

        async function addGame(){if(!isAdmin) return;const nameInput=document.getElementById('new-game-name');const gameName=nameInput.value.trim();if(!gameName){alert('Bitte Spielnamen eingeben');return}try{await fetch('/api/games',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({game:{name:gameName,cover_url:'',owned:false}})});nameInput.value=''}catch(error){console.error('Add game failed',error);alert(`Spiel hinzufügen fehlgeschlagen: ${error.message}`)}}

        async function changeCategory(gameName,category){if(!isAdmin) return;try{await fetch('/api/game/update',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({gameName,updates:{overlay:category}})})}catch(error){console.error('Category change failed',error);alert(`Kategorieänderung fehlgeschlagen: ${error.message}`)}}

        async function toggleOwned(gameName,event){event.stopPropagation();if(!isAdmin) return;try{const currentOwned = gameData.games[gameName]?.owned || false;await fetch('/api/game/update',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({gameName,updates:{owned:!currentOwned}})})}catch(error){console.error('Owned toggle failed',error);alert(`Besitzstatus ändern fehlgeschlagen: ${error.message}`)}}

        async function removeGame(gameName,event){event.stopPropagation();if(!isAdmin) return; if(!confirm(`Möchten Sie "${gameName}" wirklich entfernen?`)) return;try{await fetch('/api/game/remove',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({gameName})})}catch(error){console.error('Remove game failed',error);alert(`Spiel entfernen fehlgeschlagen: ${error.message}`)}}

        async function searchIGDB(){if(!isAdmin) return; const query=document.getElementById('new-game-name').value.trim(); if(!query) return; try{const response=await fetch('/api/igdb/search',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({query})}); if(!response.ok){const err=await response.json();throw new Error(err.error||'Search failed')} const results=await response.json(); displayIGDBResults(results);}catch(error){console.error('IGDB search failed',error);alert(`Suche fehlgeschlagen: ${error.message}`)}}

        function displayIGDBResults(results){const container=document.getElementById('igdb-results-container');const resultsSection=document.getElementById('igdb-search-results');container.innerHTML='';resultsSection.style.display='block';results.forEach(game=>{const gameDiv=document.createElement('div');gameDiv.className='igdb-result';let coverUrl=game.cover_url||'https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';if(!coverUrl.startsWith('http')) coverUrl='https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png';gameDiv.innerHTML=`<img src="${coverUrl}" alt="${game.name}" onerror="this.onerror=null;this.src='https://images.igdb.com/igdb/image/upload/t_cover_big/nocover.png'"><div><div>${game.name}</div><button onclick="addIGDBGame('${game.name.replace(/'/g,"\\'")}','${coverUrl}')">Hinzuf\u00fcgen</button></div>`;container.appendChild(gameDiv)})}

        function addIGDBGame(gameName,coverUrl){document.getElementById('new-game-name').value=gameName; if(!isAdmin) return; fetch('/api/games',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({game:{name:gameName.trim(),cover_url:coverUrl,owned:false}})}).then(response=>{if(!response.ok) throw new Error('Failed to add game');document.getElementById('igdb-search-results').style.display='none'}).catch(error=>{console.error('Add game failed',error);alert(`Spiel hinzufügen fehlgeschlagen: ${error.message}`)})}

        /* Drag and drop kept */
        let draggedGame=null;
        function handleDragStart(event){if(!isAdmin){event.preventDefault();return}draggedGame=this.getAttribute('data-game');this.classList.add('dragging');event.dataTransfer.effectAllowed='move'}
        function handleDragEnd(){this.classList.remove('dragging');draggedGame=null}
        function handleDragOver(event){if(!isAdmin) return;event.preventDefault();this.classList.add('drop-target')}
        function handleDragLeave(){if(!isAdmin) return;this.classList.remove('drop-target')}
        function handleDrop(event){event.preventDefault();this.classList.remove('drop-target');if(!draggedGame || !isAdmin) return;const targetCategory=this.parentElement.getAttribute('data-category');changeCategory(draggedGame,targetCategory)}

        function showError(message){error.style.display='block';errorMessage.textContent=message;dashboard.style.display='none';loading.style.display='none'}
        function hideError(){error.style.display='none'}

        document.addEventListener('DOMContentLoaded',init);
        document.querySelectorAll('.game-container').forEach(container=>{container.addEventListener('dragover',handleDragOver);container.addEventListener('dragleave',handleDragLeave);container.addEventListener('drop',handleDrop)});

        window.addEventListener('load',()=>{const hash=window.location.hash.substring(1);if(hash.includes('access_token')){const token=hash.split('=')[1].split('&')[0];handleTwitchLoginToken(token)}});
    </script>
</body>

</html>